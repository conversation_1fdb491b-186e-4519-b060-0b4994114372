#!/bin/bash
set -euo pipefail

# =======================
# Config
# =======================
APP_NAME="NanoTrading"
DISPLAY_NAME="NanoTrading V9"
VERSION="0.0.9"
BUNDLE_ID="com.nanotrading.app"

ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_OUT="$ROOT/dist/${APP_NAME}.app"

FILE_BASE="${APP_NAME}-${VERSION}"
PKG_NAME="${FILE_BASE}.pkg"
DMG_NAME="${FILE_BASE}.dmg"



WORK="$ROOT/.build_pkg_dmg"
DMG_ROOT="$WORK/dmg_root"

GREEN='\033[0;32m'; YELLOW='\033[1;33m'; RED='\033[0;31m'; NC='\033[0m'
say(){ echo -e "${GREEN}•${NC} $*"; }
warn(){ echo -e "${YELLOW}⚠${NC} $*"; }
fail(){ echo -e "${RED}❌${NC} $*" >&2; exit 1; }

echo "==============================================="
echo "Build ${DISPLAY_NAME} ${VERSION} → .app, .pkg, .dmg"
echo "==============================================="

# 0) Outils requis (macOS)
command -v pkgbuild >/dev/null 2>&1 || fail "pkgbuild introuvable (installe Xcode Command Line Tools : xcode-select --install)"
command -v hdiutil  >/dev/null 2>&1 || fail "hdiutil introuvable (macOS requis)."

# 1) venv + deps
if [ -z "${VIRTUAL_ENV:-}" ]; then
  say "Création/activation du venv .venv …"
  python3 -m venv "$ROOT/.venv" || fail "Impossible de créer le venv"
  # shellcheck disable=SC1091
  source "$ROOT/.venv/bin/activate"
else
  say "Venv actif: $VIRTUAL_ENV"
fi

say "Mise à jour pip/setuptools/wheel …"
python -m pip install -U pip setuptools wheel

if [ -f "$ROOT/requirements.txt" ]; then
  say "Installation des dépendances (requirements.txt) …"
  python -m pip install -r "$ROOT/requirements.txt"
else
  say "Installation minimale (pyinstaller + libs usuelles) …"
  python -m pip install pyinstaller PySide6 pandas numpy matplotlib plotly pyqtgraph python-dotenv packaging
fi

# 2) Clean build/artefacts précédents
say "Nettoyage build/dist/spec …"
rm -rf "$ROOT/build" "$ROOT/dist" "$ROOT/${APP_NAME}.spec" "$WORK"
mkdir -p "$WORK" "$DMG_ROOT"

# 3) Vérifications préalables
if [ ! -f "$ROOT/main.py" ]; then
  fail "main.py introuvable dans $ROOT"
fi

if [ ! -d "$ROOT/icons" ]; then
  fail "Dossier icons/ introuvable dans $ROOT"
fi

# Build avec PyInstaller (essai 1: minimal, SANS --collect-all)
BUILD_OK=0

say "PyInstaller (essai 1 - minimal, avec icône)…"

ICON_ICNS="$ROOT/logo.icns"   # icône à la racine du projet
if [ ! -f "$ICON_ICNS" ]; then
  fail "Icône introuvable: $ICON_ICNS"
fi

set +e
pyinstaller \
  --noconfirm \
  --windowed \
  --name "$APP_NAME" \
  --osx-bundle-identifier "$BUNDLE_ID" \
  --icon "$ICON_ICNS" \
  --add-data "icons:icons" \
  --hidden-import "PySide6.QtCore" \
  --hidden-import "PySide6.QtWidgets" \
  --hidden-import "PySide6.QtGui" \
  main.py
rc=$?
set -e



if [ $rc -ne 0 ] || [ ! -d "$APP_OUT" ]; then
  warn "Essai 1 échoué. Tentative 2 en excluant Qt3D (évite le bug de symlinks)…"
  rm -rf "$ROOT/build" "$ROOT/dist" "$ROOT/${APP_NAME}.spec"
  set +e
  pyinstaller \
    --noconfirm \
    --windowed \
    --name "$APP_NAME" \
    --osx-bundle-identifier "$BUNDLE_ID" \
    --icon "$ICON_ICNS" \
    --add-data "icons:icons" \
    --hidden-import "PySide6.QtCore" \
    --hidden-import "PySide6.QtWidgets" \
    --hidden-import "PySide6.QtGui" \
    --exclude-module PySide6.Qt3DCore \
    --exclude-module PySide6.Qt3DRender \
    --exclude-module PySide6.Qt3DAnimation \
    --exclude-module PySide6.Qt3DInput \
    --exclude-module PySide6.Qt3DExtras \
    --exclude-module PySide6.Qt3DLogic \
    main.py
  rc=$?
  set -e
  if [ $rc -ne 0 ] || [ ! -d "$APP_OUT" ]; then
    fail "PyInstaller a échoué (même avec exclusion Qt3D). Regarde l’erreur ci-dessus."
  fi
fi

# 4) Assainir l’app: xattr/chmod/codesign ad-hoc
say "Unquarantine, chmod +x, ad-hoc codesign …"
xattr -dr com.apple.quarantine "$APP_OUT" 2>/dev/null || true
chmod -R a+rX "$APP_OUT"
find "$APP_OUT/Contents/MacOS" -type f -exec chmod +x {} \; 2>/dev/null || true
# Vérifier Mach-O (pas ELF)
EXEC=$(/usr/libexec/PlistBuddy -c 'Print :CFBundleExecutable' "$APP_OUT/Contents/Info.plist" 2>/dev/null || echo "$APP_NAME")
if file "$APP_OUT/Contents/MacOS/$EXEC" | grep -q ELF; then
  fail "L’exécutable est ELF (Linux). Rebuild sur macOS pour obtenir un binaire Mach-O."
fi
codesign --force --deep -s - "$APP_OUT" || warn "codesign ad-hoc a échoué (je continue)."

# 5) Construire le .pkg (component → /Applications)
say "Construction PKG → ${PKG_NAME}"
rm -f "$PKG_NAME"
pkgbuild \
  --identifier "$BUNDLE_ID" \
  --version "$VERSION" \
  --install-location "/Applications" \
  --component "$APP_OUT" \
  "$PKG_NAME"

# 6) Construire le .dmg (drag & drop)
say "Construction DMG → ${DMG_NAME}"
rm -f "$DMG_NAME"
cp -R "$APP_OUT" "$DMG_ROOT/${APP_NAME}.app"
ln -sf /Applications "$DMG_ROOT/Applications"
hdiutil create \
  -volname "${DISPLAY_NAME} ${VERSION}" \
  -srcfolder "$DMG_ROOT" \
  -ov -format UDZO \
  "$DMG_NAME" >/dev/null

echo "==============================================="
echo "✅ Terminé."
echo " - $(du -h "${PKG_NAME}" | awk '{print $1}')  ${PKG_NAME}"
echo " - $(du -h "${DMG_NAME}" | awk '{print $1}')  ${DMG_NAME}"
echo "==============================================="
echo "Astuce: pour voir les logs runtime :"
echo "  \"$APP_OUT/Contents/MacOS/${EXEC}\""
