#!/bin/bash
# =============================================================================
# NanoTrading V7 - macOS Uninstall Script
# =============================================================================
# This script UNINSTALLS NanoTrading and all shortcuts/configs (macOS)
# Usage: ./uninstall_mac.sh
# =============================================================================

APP_NAME="NanoTrading"
APP_DIR="$HOME/Applications/$APP_NAME"
DESKTOP_DIR="$HOME/Desktop"
APP_BUNDLE="$APP_DIR/${APP_NAME}.app"
SHORTCUT_DESKTOP_APP="$DESKTOP_DIR/${APP_NAME} V7.app"
SHORTCUT_DESKTOP_SH="$DESKTOP_DIR/${APP_NAME} V7.sh"
SHORTCUT_APPS_APP="/Applications/${APP_NAME} V7.app"
SHORTCUT_APPS_SH="/Applications/${APP_NAME} V7.sh"

# Colors
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'
INFO="${BLUE}[INFO]${NC}"; SUCCESS="${GREEN}[SUCCESS]${NC}"; WARNING="${YELLOW}[WARNING]${NC}"; ERROR="${RED}[ERROR]${NC}"

echo "============================================================================="
echo "                  NanoTrading V7 - macOS UNINSTALLER"
echo "============================================================================="
echo ""

read -p "⚠️  This will remove all NanoTrading files and configs from this user. Continue? [y/N] " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
  echo -e "$WARNING Uninstall cancelled."
  exit 0
fi

echo -e "$INFO Removing application directory: $APP_DIR"
rm -rf "$APP_DIR"

echo -e "$INFO Removing desktop shortcuts..."
rm -f "$SHORTCUT_DESKTOP_APP"
rm -f "$SHORTCUT_DESKTOP_SH"

echo -e "$INFO Removing Applications folder shortcuts..."
sudo rm -f "$SHORTCUT_APPS_APP"
sudo rm -f "$SHORTCUT_APPS_SH"

echo -e "$INFO Checking for any virtualenv cache (pip)..."
rm -rf "$HOME/Library/Caches/pip"

echo -e "$INFO Removing possible config/log files..."
rm -f "$HOME/.nanotrading*" "$HOME/nanotrading.log" "$HOME/.config/nanotrading*" 2>/dev/null || true

echo -e "$SUCCESS All NanoTrading files and shortcuts have been removed."

echo ""
echo "============================================================================="
echo -e "$SUCCESS NanoTrading V7 uninstall completed!"
echo "============================================================================="
echo ""

exit 0
#!/bin/bash
# =============================================================================
# NanoTrading V7 - macOS Uninstall Script
# =============================================================================
# This script UNINSTALLS NanoTrading and all shortcuts/configs (macOS)
# Usage: ./uninstall_mac.sh
# =============================================================================

APP_NAME="NanoTrading"
APP_DIR="$HOME/Applications/$APP_NAME"
DESKTOP_DIR="$HOME/Desktop"
APP_BUNDLE="$APP_DIR/${APP_NAME}.app"
SHORTCUT_DESKTOP_APP="$DESKTOP_DIR/${APP_NAME} V7.app"
SHORTCUT_DESKTOP_SH="$DESKTOP_DIR/${APP_NAME} V7.sh"
SHORTCUT_APPS_APP="/Applications/${APP_NAME} V7.app"
SHORTCUT_APPS_SH="/Applications/${APP_NAME} V7.sh"

# Colors
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'
INFO="${BLUE}[INFO]${NC}"; SUCCESS="${GREEN}[SUCCESS]${NC}"; WARNING="${YELLOW}[WARNING]${NC}"; ERROR="${RED}[ERROR]${NC}"

echo "============================================================================="
echo "                  NanoTrading V7 - macOS UNINSTALLER"
echo "============================================================================="
echo ""

read -p "⚠️  This will remove all NanoTrading files and configs from this user. Continue? [y/N] " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
  echo -e "$WARNING Uninstall cancelled."
  exit 0
fi

echo -e "$INFO Removing application directory: $APP_DIR"
rm -rf "$APP_DIR"

echo -e "$INFO Removing desktop shortcuts..."
rm -f "$SHORTCUT_DESKTOP_APP"
rm -f "$SHORTCUT_DESKTOP_SH"

echo -e "$INFO Removing Applications folder shortcuts..."
sudo rm -f "$SHORTCUT_APPS_APP"
sudo rm -f "$SHORTCUT_APPS_SH"

echo -e "$INFO Checking for any virtualenv cache (pip)..."
rm -rf "$HOME/Library/Caches/pip"

echo -e "$INFO Removing possible config/log files..."
rm -f "$HOME/.nanotrading*" "$HOME/nanotrading.log" "$HOME/.config/nanotrading*" 2>/dev/null || true

echo -e "$SUCCESS All NanoTrading files and shortcuts have been removed."

echo ""
echo "============================================================================="
echo -e "$SUCCESS NanoTrading V7 uninstall completed!"
echo "============================================================================="
echo ""

exit 0
