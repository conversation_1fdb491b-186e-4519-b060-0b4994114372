#!/usr/bin/env python3
"""
Check that every indicator defined in indicator.py has at least one default
(setting) returned by indicator_settings.get_defaults().

Exit codes:
- 0: all indicators have non-empty defaults (OK)
- 1: at least one indicator has empty defaults (TO COMPLETE)

This is a lightweight coverage check for decision/calculation defaults to
help ensure the UI schema generation has material to work with.
"""
from __future__ import annotations

import sys
import re
import ast
from pathlib import Path
from typing import Dict, List, Tuple

ROOT = Path(__file__).resolve().parents[1]

NUMERIC_PARAM_KEYS = {
    "window", "window_fast", "window_slow", "window_dev", "signal",
    "lbp", "pow1", "pow2", "window_atr", "roc1", "roc2", "roc3", "roc4",
    "window1", "window2", "window3", "window4",
}


def _read_file(p: Path) -> str:
    return p.read_text(encoding="utf-8")


def _extract_assigned_dict_via_ast(source: str, var_name: str) -> dict:
    """Parse Python source and return the dict assigned to var_name via AST."""
    try:
        tree = ast.parse(source)
    except SyntaxError as e:
        return {}
    for node in tree.body:
        if isinstance(node, ast.Assign):
            # support multiple targets: a = {...}
            for tgt in node.targets:
                if isinstance(tgt, ast.Name) and tgt.id == var_name:
                    try:
                        return ast.literal_eval(node.value)
                    except Exception:
                        return {}
        elif isinstance(node, ast.AnnAssign):
            # support annotated assignment: a: Dict[...] = {...}
            if isinstance(node.target, ast.Name) and node.target.id == var_name and node.value is not None:
                try:
                    return ast.literal_eval(node.value)
                except Exception:
                    return {}
    return {}


def _extract_indicators_and_formulas(indicator_py: Path) -> Dict[str, str]:
    src = _read_file(indicator_py)
    indicators_root = _extract_assigned_dict_via_ast(src, "indicators")
    out: Dict[str, str] = {}
    for _cat, mapping in indicators_root.items():
        for name, formula_str in mapping.items():
            out[name] = formula_str
    return out


def _extract_decision_defaults(settings_py: Path) -> Dict[str, dict]:
    src = _read_file(settings_py)
    decision_defaults = _extract_assigned_dict_via_ast(src, "DECISION_DEFAULTS")
    print(f"Parsed {len(decision_defaults)} DECISION_DEFAULTS entries")
    print(f"Sample keys: {list(decision_defaults.keys())[:10]}")
    return decision_defaults


def _infer_numeric_params_from_formula(formula: str) -> Dict[str, float]:
    params: Dict[str, float] = {}
    for key in NUMERIC_PARAM_KEYS:
        pattern = rf"{re.escape(key)}\s*=\s*([0-9]*\.?[0-9]+)"
        m = re.search(pattern, formula)
        if m:
            val_str = m.group(1)
            try:
                val = float(val_str) if "." in val_str else int(val_str)
            except Exception:
                try:
                    val = float(val_str)
                except Exception:
                    continue
            params[key] = val
    return params


def main() -> int:
    indicator_py = ROOT / "indicator.py"
    settings_py = ROOT / "indicator_settings.py"

    if not indicator_py.exists() or not settings_py.exists():
        print("ERROR: expected files indicator.py and indicator_settings.py not found.")
        return 2

    ind_map = _extract_indicators_and_formulas(indicator_py)
    decision_defaults = _extract_decision_defaults(settings_py)

    names: List[str] = sorted(ind_map.keys())
    missing: List[str] = []

    print(f"Checking indicator_settings defaults coverage for {len(names)} indicators...\n")
    print(f"Parsed DECISION_DEFAULTS entries: {len(decision_defaults)}")
    if decision_defaults:
        sample_keys = list(decision_defaults.keys())[:10]
        print(f"Sample keys: {sample_keys}\n")

    for name in names:
        has_decision = name in decision_defaults
        inferred = _infer_numeric_params_from_formula(ind_map[name])
        has_calc = bool(inferred)
        ok = has_decision or has_calc
        if not ok:
            missing.append(name)
        status = "OK" if ok else "MISSING"
        info = []
        if has_decision:
            info.append("decision")
        if has_calc:
            info.append("calc")
        info_s = ", ".join(info) if info else "none"
        print(f"- {name}: {status} ({info_s})")

    print("\nSummary:")
    print(f"  OK: {len(names) - len(missing)}")
    print(f"  MISSING: {len(missing)}")

    if missing:
        print("\nIndicators with empty defaults (to complete):")
        for m in missing:
            print(f"  - {m}")
        return 1
    else:
        print("\nAll indicators have non-empty defaults (decision and/or calc). ✅")
        return 0


if __name__ == "__main__":
    sys.exit(main())
