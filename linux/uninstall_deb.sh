#!/bin/bash

APP_NAME=nanotrading

echo "🧹 Suppression de $APP_NAME..."

# Désinstallation via dpkg
if dpkg -l | grep -q "$APP_NAME"; then
    sudo dpkg -r "$APP_NAME"
else
    echo "ℹ️ Le paquet $APP_NAME n'est pas installé via dpkg."
fi

# Supprimer manuellement les raccourcis et icônes s'ils existent
DESKTOP_FILE="$HOME/.local/share/applications/nanotrading.desktop"
ICON_FILE="$HOME/.local/share/icons/nanotrading.png"

[ -f "$DESKTOP_FILE" ] && rm "$DESKTOP_FILE" && echo "🗑️ Raccourci supprimé : $DESKTOP_FILE"
[ -f "$ICON_FILE" ] && rm "$ICON_FILE" && echo "🗑️ Icône supprimée : $ICON_FILE"

# Supprimer l'app installée dans /usr/local/bin si encore présente
BIN_PATH="/usr/local/bin/$APP_NAME"
[ -f "$BIN_PATH" ] && sudo rm "$BIN_PATH" && echo "🗑️ Exécutable supprimé : $BIN_PATH"

echo "✅ Désinstallation terminée."
