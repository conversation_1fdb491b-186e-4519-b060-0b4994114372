#!/bin/bash
set -e  # Stopper si erreur

APP_NAME=nanotrading
VERSION=9.0.1
ARCH=amd64
BUILD_DIR=deb_build
INSTALL_PREFIX=/usr/local/bin
APP_DIR=/opt/${APP_NAME}

ICON_SOURCE=icons/nano_trading_logo.png
ICON_NAME=nanotrading.png
ICON_DEST_DIR=usr/share/icons/hicolor/128x128/apps

# Nettoyage
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR/DEBIAN"
mkdir -p "$BUILD_DIR/$INSTALL_PREFIX"
mkdir -p "$BUILD_DIR/usr/share/applications"
mkdir -p "$BUILD_DIR/$ICON_DEST_DIR"
mkdir -p "$BUILD_DIR$APP_DIR"

# Compilation avec PyInstaller (onedir avec options Ubuntu)
echo "🔧 Compilation avec PyInstaller..."
pyinstaller --onedir --name "$APP_NAME" \
  --add-data "icons:icons" \
  --hidden-import=PySide6.QtCore \
  --hidden-import=PySide6.QtGui \
  --hidden-import=PySide6.QtWidgets \
  --exclude-module PySide6.Qt3DCore \
  --exclude-module PySide6.Qt3DRender \
  --exclude-module PySide6.Qt3DAnimation \
  --exclude-module PySide6.Qt3DInput \
  --exclude-module PySide6.Qt3DExtras \
  --noconfirm \
  main.py

# Copie de tout le dossier de l'application
cp -r "dist/$APP_NAME/." "$BUILD_DIR$APP_DIR/"

# Vérification que l’exécutable a bien été généré
if [ ! -f "$BUILD_DIR$APP_DIR/$APP_NAME" ]; then
  echo "❌ ERREUR : L'exécutable $APP_NAME est introuvable dans $BUILD_DIR$APP_DIR/"
  echo "➡️ Vérifiez que PyInstaller a bien compilé l'application."
  exit 1
fi
chmod 755 "$BUILD_DIR$APP_DIR/$APP_NAME"

# Vérification et correction des bibliothèques Python
echo "🔍 Vérification des bibliothèques..."
mkdir -p "$BUILD_DIR$APP_DIR/_internal"
if [ ! -f "$BUILD_DIR$APP_DIR/_internal/libpython3.12.so" ]; then
    echo "⚠️  libpython3.12.so manquante, recherche sur le système..."
    PYTHON_LIB=$(find /usr/lib -name "libpython3.12.so*" 2>/dev/null | head -1)
    if [ -n "$PYTHON_LIB" ]; then
        echo "📋 Copie de $PYTHON_LIB"
        cp "$PYTHON_LIB" "$BUILD_DIR$APP_DIR/_internal/"
    fi
fi

# Créer un script de lancement robuste avec copie d'icônes
cat > "$BUILD_DIR/$INSTALL_PREFIX/${APP_NAME}-launcher.sh" <<EOF
#!/bin/bash
# NanoTrading Launcher Script

# Définir les variables d'environnement nécessaires
export DISPLAY=\${DISPLAY:-:0}
export QT_QPA_PLATFORM=\${QT_QPA_PLATFORM:-xcb}

# Définir le répertoire de travail correct (CORRECTION CRITIQUE)
cd "$APP_DIR"
echo "[LAUNCHER] Working directory set to: \$(pwd)"

# Vérifier que les icônes sont accessibles
ICON_SRC="$APP_DIR/icons"
if [ -d "\$ICON_SRC" ]; then
    ICON_COUNT=\$(find "\$ICON_SRC" -name "*.png" 2>/dev/null | wc -l)
    echo "[LAUNCHER] ✅ Found \$ICON_COUNT icons in app directory"
else
    echo "[LAUNCHER] ⚠️ Icons directory not found: \$ICON_SRC"
fi

# Lancer l'application depuis son répertoire
exec "$APP_DIR/$APP_NAME" "\$@"
EOF

chmod 755 "$BUILD_DIR/$INSTALL_PREFIX/${APP_NAME}-launcher.sh"

# Copie de l'icône principale
cp "$ICON_SOURCE" "$BUILD_DIR/$ICON_DEST_DIR/$ICON_NAME"

# Copier toutes les icônes PNG dans le paquet
echo "📁 Copie de TOUTES les icônes dans le paquet..."
mkdir -p "$BUILD_DIR$APP_DIR/icons"
if [ -d "./icons" ]; then
    cp -v ./icons/*.png "$BUILD_DIR$APP_DIR/icons/" 2>/dev/null || true
    COPIED_COUNT=$(find "$BUILD_DIR$APP_DIR/icons" -name "*.png" 2>/dev/null | wc -l)
    SOURCE_COUNT=$(find "./icons" -name "*.png" 2>/dev/null | wc -l)
    echo "✅ Icônes copiées dans le paquet: $COPIED_COUNT/$SOURCE_COUNT"
    echo "📋 Icônes présentes dans /opt/nanotrading/icons:"
    ls -la "$BUILD_DIR$APP_DIR/icons/" | grep "\.png" | head -10
else
    echo "❌ ERREUR: Dossier ./icons non trouvé!"
    exit 1
fi

# Desktop entry
cat > "$BUILD_DIR/usr/share/applications/$APP_NAME.desktop" <<EOF
[Desktop Entry]
Name=NanoTrading
Comment=Automated trading app
Exec=$INSTALL_PREFIX/${APP_NAME}-launcher.sh
Icon=nanotrading
Terminal=false
Type=Application
Categories=Finance;Office;
StartupNotify=true
StartupWMClass=NanoTrading
EOF

# Fichier DEBIAN/control
cat > "$BUILD_DIR/DEBIAN/control" <<EOF
Package: $APP_NAME
Version: $VERSION
Section: base
Priority: optional
Architecture: $ARCH
Depends: libc6, libgcc-s1, libqt6core6, libqt6gui6, libqt6widgets6, libxcb-cursor0, libxkbcommon-x11-0, libxkbcommon0, libxcb1, libxcb-render0, libxcb-shm0, libxcb-xinerama0, libxcb-icccm4, libxcb-image0, libxcb-keysyms1, libxcb-render-util0, libxcb-xkb1, libegl1, libopengl0, libgl1-mesa-dri, libglu1-mesa, libdbus-1-3, fonts-dejavu-core
Maintainer: NANOVERSUM <<EMAIL>>
Description: NanoTrading - Automated DCA Trading Bot
 NanoTrading is an advanced automated Dollar Cost Averaging (DCA) trading bot
 designed for cryptocurrency markets. It provides intelligent trading strategies,
 real-time market analysis, and comprehensive portfolio management.
EOF

# Script post-installation
cat > "$BUILD_DIR/DEBIAN/postinst" <<EOF
#!/bin/bash
set -e

# Mettre à jour le cache des icônes
if command -v gtk-update-icon-cache >/dev/null 2>&1; then
    gtk-update-icon-cache -f -t /usr/share/icons/hicolor/ 2>/dev/null || true
fi

# Mettre à jour la base de données des applications
if command -v update-desktop-database >/dev/null 2>&1; then
    update-desktop-database /usr/share/applications/ 2>/dev/null || true
fi

# Créer l'icône sur le bureau pour chaque utilisateur
for user_home in /home/<USER>
    if [ -d "\$user_home" ] && [ -d "\$user_home/Desktop" ]; then
        username=\$(basename "\$user_home")
        desktop_file="\$user_home/Desktop/$APP_NAME.desktop"

        cat > "\$desktop_file" <<DESKTOP_EOF
[Desktop Entry]
Name=NanoTrading
Comment=Automated trading app
Exec=$INSTALL_PREFIX/${APP_NAME}-launcher.sh
Icon=nanotrading
Terminal=false
Type=Application
Categories=Finance;Office;
StartupNotify=true
StartupWMClass=NanoTrading
DESKTOP_EOF

        chown "\$username:\$username" "\$desktop_file" 2>/dev/null || true
        chmod 755 "\$desktop_file" 2>/dev/null || true
    fi
done

# Créer un alias global
ln -sf "$INSTALL_PREFIX/${APP_NAME}-launcher.sh" "$INSTALL_PREFIX/nanotrading" 2>/dev/null || true

echo "NanoTrading installation completed successfully!"
echo "You can launch it from:"
echo "  - Applications menu"
echo "  - Desktop icon"
echo "  - Terminal: nanotrading"
EOF

chmod 755 "$BUILD_DIR/DEBIAN/postinst"

# Construction du paquet
echo "📦 Création du paquet .deb..."
dpkg-deb --build "$BUILD_DIR" "${APP_NAME}_${VERSION}_${ARCH}.deb"
echo "✅ Paquet créé : ${APP_NAME}_${VERSION}_${ARCH}.deb"


